"""
FRACTIONAL ADAPTIVE SUPERRESOLUTION WAVELET (SUPERLET) TRANSFORM

Author:
    <PERSON>

Date:
    April 2019

Description:
    Computes the adaptive superresolution wavelet (superlet) transform on input
    data to produce a time–frequency representation.  For each frequency of
    interest, the closest integer order from the order interval will be chosen
    to produce each superlet. A superlet is a set of wavelets with the same
    center frequency but different number of cycles.

Reference:
    Moca, V.V., Nagy-<PERSON>can, A., B<PERSON>, H., <PERSON>, R.C.
    “Time-frequency super-resolution with superlets”
    Nature Communications 11, 344 (2020)
    https://www.nature.com/articles/s41467-020-20539-9

Notes:
  - If the input data consists of multiple buffers, a wavelet spectrum will be
    computed for each buffer and averaged to produce the final result.
  - If the order parameter `o` is None or empty, this function returns the
    standard CWT (one wavelet per frequency).

Inputs:
  input_data : array_like, shape (n_buffers, n_samples) or (n_samples,)
      time series data
  Fs         : float
      sampling frequency in Hz
  F          : array_like
      frequencies of interest
  c1         : int
      number of initial wavelet cycles
  o          : tuple or list of two floats, optional
      interval of superresolution orders
  mult       : int
      multiplicative superresolution flag (0=additive, !=0=multiplicative)

Returns:
  wtresult : ndarray, shape (len(F), n_samples)
      superlet time–frequency spectrum
"""

import numpy as np
from scipy.signal import fftconvolve

def gauss(t, sd):
    """
    Compute the gaussian coefficient for the desired time point t and standard deviation sd.
    """
    cnorm = 1 / (sd * np.sqrt(2 * np.pi))
    res = cnorm * np.exp(-(t ** 2) / (2 * sd ** 2))
    return res

def bw_cf(t, bw, cf):
    """
    Compute the complex wavelet coefficients for the desired time point t, bandwidth bw and center frequency cf.
    """
    cnorm = 1 / (bw * np.sqrt(2 * np.pi))
    exp1 = cnorm * np.exp(-(t ** 2) / (2 * bw ** 2))
    res = np.exp(2j * np.pi * cf * t) * exp1
    return res

def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet for the desired center frequency Fc with Nc cycles, with a sampling frequency Fs.
    """
    # Avoid zero frequency by setting minimum frequency to 0.001
    if np.abs(Fc) < 1e-10:
        Fc = 0.001 if Fc >= 0 else -0.001

    sd = (Nc / 2) * (1 / np.abs(Fc)) / 2.5
    wl = int(2 * np.floor(np.fix(6 * sd * Fs) / 2) + 1)
    w = np.zeros(wl, dtype=np.complex128)
    gi = 0
    off = int(np.fix(wl / 2))

    for i in range(wl):
        t = (i - off) / Fs
        w[i] = bw_cf(t, sd, Fc)
        gi += gauss(t, sd)

    w /= gi
    return w

def is_fractional(x):
    """
    Tell if a number is an integer or a fractional.
    """
    return np.fix(x) != x

def faslt(input_data, Fs, F, c1, o, mult):
    """
    Computes the adaptive superresolution wavelet (superlet) transform on input data to produce a time-frequency representation.
    
    For each frequency of interest, the closest integer order from the order interval will be chosen to produce each superlet.
    A superlet is a set of wavelets with the same center frequency but different number of cycles.
    
    If the input data consists of multiple buffers, a wavelet spectrum will be computed for each of the buffers and averaged
    to produce the final result.
    
    If the order parameter (o) is empty, this function will return the standard CWT (one wavelet per frequency of interest).
    
    Parameters:
    - input_data: [buffers x samples] matrix or 1D array
    - Fs: sampling frequency in Hz
    - F: frequency-of-interest array
    - c1: number of initial wavelet cycles
    - o: [1 x 2] interval of superresolution orders (optional, can be None or empty)
    - mult: specifies the use of multiplicative superresolution (0 - additive, != 0 - multiplicative)
    
    Returns:
    - wtresult: [frequencies x samples] superlet spectrum
    """
    if len(F) == 0:
        raise ValueError('frequencies not defined')
    
    if input_data.size == 0:
        raise ValueError('input is empty')
    
    # Handle input shape
    if input_data.ndim == 1:
        input_data = input_data.reshape(1, -1)
    elif input_data.ndim > 2:
        raise ValueError('Input must be 1D or 2D')
    
    Nbuffers, Npoints = input_data.shape
    
    # Handle order parameter
    if o is None or len(o) == 0:
        order_frac = np.ones(len(F))
        order_int = order_frac
    else:
        if len(o) != 2:
            raise ValueError('Order interval must be a 2-element array')
        order_frac = np.linspace(o[0], o[1], len(F))
        order_int = np.ceil(order_frac)
    
    # Create wavelet sets
    wavelets = [[] for _ in range(len(F))]
    padding = 0
    
    for i_freq in range(len(F)):
        for i_ord in range(1, int(order_int[i_freq]) + 1):
            if mult != 0:
                n_cyc = i_ord * c1
            else:
                n_cyc = i_ord + c1
            
            wavelet = cxmorlet(F[i_freq], n_cyc, Fs)
            wavelets[i_freq].append(wavelet)
            
            padding = max(padding, int(np.fix(len(wavelet) / 2)))

    # Buffers
    buffer = np.zeros(int(Npoints + 2 * padding))
    wtresult = np.zeros((len(F), Npoints))

    bufbegin = int(padding)
    bufend = int(padding + Npoints)
    
    # Loop over input buffers
    for i_buf in range(Nbuffers):
        for i_freq in range(len(F)):
            # Pooling buffer, starts with 1 for geometric mean
            temp = np.ones(Npoints)
            
            # Fill central part of buffer
            buffer[bufbegin:bufend] = input_data[i_buf, :]
            
            # Number of integer wavelets
            n_wavelets = np.floor(order_frac[i_freq])
            
            # Convolve with each integer wavelet
            for i_ord in range(1, int(n_wavelets) + 1):
                tempcx = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')
                temp = temp * (2 * np.abs(tempcx[bufbegin:bufend]) ** 2)
            
            # Handle fractional part
            if is_fractional(order_frac[i_freq]) and len(wavelets[i_freq]) > int(n_wavelets):
                i_ord = int(order_int[i_freq])
                exponent = order_frac[i_freq] - np.fix(order_frac[i_freq])
                
                tempcx = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')
                temp = temp * ((2 * np.abs(tempcx[bufbegin:bufend]) ** 2) ** exponent)
            
            # Geometric mean
            root = 1 / order_frac[i_freq]
            temp = temp ** root
            
            # Accumulate
            wtresult[i_freq, :] += temp
    
    # Average over buffers
    wtresult /= Nbuffers
    
    return wtresult