# FASLTCX Migration Guide

## Overview

This guide documents the migration from the original Superlet implementation to the FASLTCX (Fast Adaptive Superlet Transform Complex) implementation in the Superlets codebase.

## Files Modified/Created

### 1. Modified Files

#### `demo_script/segy_superlet_dlogst_comparison.py`
- **Purpose**: Updated the original reference script to use FASLTCX instead of superletcx
- **Key Changes**:
  - Added import for `fasltcx` module
  - Created wrapper function `superlets_fasltcx_wrapper()` for interface compatibility
  - Updated parameter names from "Superlet" to "FASLTCX" in GUI
  - Updated plot titles and variable names to reflect FASLTCX usage
  - Updated output filenames to include "fasltcx" identifier

### 2. New Files

#### `demo_script/segy_fasltcx_dlogst_comparison.py`
- **Purpose**: New demo script specifically designed for FASLTCX implementation
- **Features**:
  - Clean implementation using FASLTCX functions
  - Same 7-column comparison plot structure as original
  - Updated documentation and comments
  - Maintains all original functionality (GUI, plotting, multi-trace support)

#### `FASLTCX_MIGRATION_GUIDE.md` (this file)
- **Purpose**: Documentation of changes and usage instructions

## Key Technical Changes

### Function Interface Mapping

| Original (superletcx) | FASLTCX | Notes |
|----------------------|---------|-------|
| `superlets(data, fs, foi, c1, ord)` | `fasltcx(input_data, Fs, F, c1, o, mult)` | Added `mult` parameter |
| `data` | `input_data` | Signal data |
| `fs` | `Fs` | Sampling frequency |
| `foi` | `F` | Frequencies of interest |
| `c1` | `c1` | Base cycles parameter |
| `ord` | `o` | Order range tuple |
| N/A | `mult` | Multiplication mode (set to `True`) |

### Wrapper Function

To maintain compatibility with the original interface, a wrapper function was created:

```python
def superlets_fasltcx_wrapper(data, fs, foi, c1, ord):
    """Wrapper function to make fasltcx compatible with superletcx interface"""
    return fasltcx(data, fs, foi, c1, ord, True)
```

The `mult=True` parameter ensures that the FASLTCX implementation uses the same cycle calculation as the original superlets (`i_ord * c1` instead of `i_ord + c1`).

## Usage Instructions

### Running the Modified Reference Script

```bash
cd demo_script
python segy_superlet_dlogst_comparison.py
```

This script now uses FASLTCX by default but maintains the same user interface and functionality.

### Running the New FASLTCX Demo Script

```bash
cd demo_script
python segy_fasltcx_dlogst_comparison.py
```

This is a clean implementation specifically designed for FASLTCX.

### Expected Behavior

Both scripts should:
1. Open a file dialog to select SEG-Y files
2. Allow selection of trace indices
3. Provide GUI for setting plot parameters and transform settings
4. Generate 7-column comparison plots showing:
   - Column 1: Original signal
   - Columns 2-4: FASLTCX (Magnitude, Voice, Magnitude×Voice)
   - Columns 5-7: DLOGST (Magnitude, Voice, Magnitude×Voice)
5. Save plots as PNG files with appropriate naming

## Parameter Compatibility

### FASLTCX Parameters
- **c1**: Base number of cycles (default: 3)
- **order min**: Minimum order value (default: 1)
- **order max**: Maximum order value (default: 10)

### DLOGST Parameters (unchanged)
- **shape**: Shape constant (default: 0.35)
- **kmax**: Maximum k value (default: 120)
- **int_val**: Intercept value (default: 35)

## Output Differences

### File Naming
- Original: `segy_superlet_dlogst_comparison_{trace_name}.png`
- FASLTCX: `segy_fasltcx_dlogst_comparison_{trace_name}.png`

### Plot Titles
- Original: "Superlet vs DLOGST Comparison"
- FASLTCX: "FASLTCX vs DLOGST Comparison"

## Verification

To verify the migration was successful:

1. **Import Test**: Both scripts should import without errors
2. **Function Test**: The wrapper function should return complex spectra
3. **Plot Test**: Generated plots should have similar structure to original
4. **Parameter Test**: GUI should show FASLTCX parameters instead of Superlet

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `fasltcx.py` is in the `code/` directory
2. **Parameter Errors**: Check that `mult=True` is set in the wrapper function
3. **Plot Differences**: Minor differences in results are expected due to implementation variations

### Dependencies

Ensure all required packages are installed:
- numpy
- matplotlib
- scipy
- segyio
- tkinter (usually included with Python)
- pyfftw (for DLOGST functionality)

## Future Considerations

- The wrapper function approach allows easy switching between implementations
- Additional FASLTCX-specific features can be added to the new demo script
- The original superletcx functionality remains available if needed
